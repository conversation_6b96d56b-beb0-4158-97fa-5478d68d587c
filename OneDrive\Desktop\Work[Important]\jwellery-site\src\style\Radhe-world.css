/* Tanishq World Component Styles */
.tanishq-world {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  font-family: 'Georgia', serif;
}

/* Header Styles */
.tanishq-header {
  text-align: center;
  margin-bottom: 40px;
}

.tanishq-title {
  font-size: 2.5rem;
  font-weight: 400;
  color: #8B4513;
  margin: 0 0 10px 0;
  letter-spacing: 0.5px;
}

.tanishq-subtitle {
  font-size: 1.1rem;
  color: #666;
  margin: 0;
  font-style: italic;
  font-weight: 300;
}

/* Grid Layout */
.tanishq-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 15px;
  height: 600px;
}

/* Individual Item Styles */
.tanishq-item {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.tanishq-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.tanishq-link {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
  color: inherit;
}

.tanishq-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.tanishq-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.tanishq-item:hover .tanishq-image {
  transform: scale(1.05);
}

/* Overlay Styles */
.tanishq-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 30px 20px 20px;
  display: flex;
  align-items: flex-end;
}

.tanishq-item-title {
  color: white;
  font-size: 1.8rem;
  font-weight: 400;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tanishq-world {
    padding: 30px 15px;
  }
  
  .tanishq-title {
    font-size: 2rem;
  }
  
  .tanishq-subtitle {
    font-size: 1rem;
  }
  
  .tanishq-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(4, 250px);
    height: auto;
    gap: 12px;
  }
  
  .tanishq-item-title {
    font-size: 1.5rem;
  }
  
  .tanishq-overlay {
    padding: 20px 15px 15px;
  }
}

@media (max-width: 480px) {
  .tanishq-world {
    padding: 20px 10px;
  }
  
  .tanishq-title {
    font-size: 1.8rem;
  }
  
  .tanishq-subtitle {
    font-size: 0.9rem;
  }
  
  .tanishq-grid {
    grid-template-rows: repeat(4, 220px);
    gap: 10px;
  }
  
  .tanishq-item-title {
    font-size: 1.3rem;
  }
}

/* Specific positioning for larger screens */
@media (min-width: 769px) {
  .tanishq-grid {
    height: 650px;
  }
  
  .wedding-item {
    grid-column: 1;
    grid-row: 1;
  }
  
  .diamond-item {
    grid-column: 2;
    grid-row: 1;
  }
  
  .gold-item {
    grid-column: 1;
    grid-row: 2;
  }
  
  .dailywear-item {
    grid-column: 2;
    grid-row: 2;
  }
}
