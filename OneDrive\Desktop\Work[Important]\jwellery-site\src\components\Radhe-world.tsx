'use client';

import React from 'react';
import '../style/Radhe-world.css';

interface RadheWorldItem {
  id: number;
  src: string;
  alt: string;
  href: string;
  title: string;
  className: string;
}

const RadheWorld: React.FC = () => {
  const RadheItems: RadheWorldItem[] = [
    {
      id: 1,
      src: "/Radhe-images/wedding.jpg",
      alt: "Wedding Collection",
      href: "#wedding",
      title: "Wedding",
      className: "wedding-item"
    },
    {
      id: 2,
      src: "/Radhe-images/diamond.jpg",
      alt: "Diamond Collection",
      href: "#diamond",
      title: "Diamond",
      className: "diamond-item"
    },
    {
      id: 3,
      src: "/Radhe-images/gold.jpg",
      alt: "Gold Collection",
      href: "#gold",
      title: "Gold",
      className: "gold-item"
    },
    {
      id: 4,
      src: "/Radhe-images/dailywear.jpg",
      alt: "Dailywear Collection",
      href: "#dailywear",
      title: "Dailywear",
      className: "dailywear-item"
    }
  ];

  const handleItemClick = (href: string) => {
    console.log(`Navigating to: ${href}`);
    // Add your navigation logic here
  };

  return (
    <div className="Radhe-world">
      <div className="Radhe-header">
        <h1 className="Radhe-title">Radhe World</h1>
        <p className="Radhe-subtitle">A companion for every occasion</p>
      </div>

      <div className="Radhe-grid">
        {RadheItems.map((item) => (
          <div key={item.id} className={`Radhe-item ${item.className}`}>
            <a
              href={item.href}
              onClick={(e) => {
                e.preventDefault();
                handleItemClick(item.href);
              }}
              className="Radhe-link"
            >
              <div className="Radhe-image-container">
                <img
                  src={item.src}
                  alt={item.alt}
                  className="Radhe-image"
                />
                <div className="Radhe-overlay">
                  <h3 className="Radhe-item-title">{item.title}</h3>
                </div>
              </div>
            </a>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RadheWorld;
